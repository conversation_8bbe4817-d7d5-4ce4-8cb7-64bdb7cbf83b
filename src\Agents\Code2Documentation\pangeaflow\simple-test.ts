// Simple test to verify the WriteChapters fix
console.log('🧪 Starting simple test...');

try {
  // Test import
  console.log('📦 Testing imports...');
  
  // Import the workflow function
  import('./flow/pangeaFlow.js').then(async (module) => {
    console.log('✅ Import successful');
    
    const { createDefaultSharedStore, executeCode2DocumentationWorkflow } = module;
    
    if (!createDefaultSharedStore || !executeCode2DocumentationWorkflow) {
      console.error('❌ Required functions not found in module');
      return;
    }
    
    console.log('✅ Functions found');
    
    // Create minimal test data
    const testShared = createDefaultSharedStore({
      user_id: 'test-user',
      selected_files: [
        ['test1.js', 'console.log("test1");'],
        ['test2.js', 'console.log("test2");']
      ],
      language: 'english',
      max_abstraction_num: 2,
      project_name: 'Simple Test',
      tutorial_id: 'test-' + Date.now()
    });
    
    console.log('📝 Test data created');
    console.log('🚀 Starting workflow execution...');
    
    // Execute workflow
    const result = await executeCode2DocumentationWorkflow(testShared);
    
    console.log('📊 Workflow completed:', {
      success: result.success,
      hasOutput: !!result.output,
      error: result.error?.message
    });
    
    if (result.success) {
      console.log('✅ Test PASSED - Workflow completed successfully');
    } else {
      console.log('❌ Test FAILED - Workflow error:', result.error?.message);
    }
    
  }).catch((error) => {
    console.error('❌ Import failed:', error.message);
  });
  
} catch (error) {
  console.error('💥 Test setup failed:', error);
}

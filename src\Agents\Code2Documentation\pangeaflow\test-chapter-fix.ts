// Test script to verify the WriteChapters fix
import { executeCode2DocumentationWorkflow, createDefaultSharedStore } from './flow/pangeaFlow';

/**
 * Test the fixed WriteChapters agent to ensure all chapters are processed
 */
async function testChapterGeneration() {
  console.log('🧪 Testing Code2Documentation WriteChapters fix...');

  try {
    // Create test data with multiple abstractions to verify all chapters are generated
    const testShared = createDefaultSharedStore({
      user_id: 'test-user',
      selected_files: [
        ['src/App.js', 'import React from "react"; function App() { return <div>Hello World</div>; } export default App;'],
        ['src/utils.js', 'export function add(a, b) { return a + b; } export function multiply(a, b) { return a * b; }'],
        ['src/components/Button.js', 'import React from "react"; export function Button({ children, onClick }) { return <button onClick={onClick}>{children}</button>; }'],
        ['src/services/api.js', 'export async function fetchData(url) { const response = await fetch(url); return response.json(); }'],
        ['src/hooks/useCounter.js', 'import { useState } from "react"; export function useCounter(initial = 0) { const [count, setCount] = useState(initial); return [count, () => setCount(c => c + 1)]; }']
      ] as [string, string][],
      language: 'english',
      max_abstraction_num: 5,
      project_name: 'Test React App',
      tutorial_id: 'test-tutorial-chapters-' + Date.now(),
      repo_url: 'https://github.com/test/react-app'
    });

    console.log('📝 Test data prepared with 5 files for multiple abstractions');

    // Execute workflow
    console.log('🚀 Starting Code2Documentation workflow...');
    const result = await executeCode2DocumentationWorkflow(testShared);

    console.log('📊 Workflow execution completed. Results:', {
      success: result.success,
      hasOutput: !!result.output,
      outputType: Array.isArray(result.output) ? 'array' : typeof result.output
    });

    if (result.success) {
      console.log('✅ Workflow completed successfully!');
      
      // Analyze the results to check chapter generation
      if (Array.isArray(result.output)) {
        const writeChaptersResults = result.output.filter((r: any) => 
          r.output && (r.output.chapterContents || r.output.currentChapter)
        );
        
        if (writeChaptersResults.length > 0) {
          const lastWriteResult = writeChaptersResults[writeChaptersResults.length - 1];
          const chapterContents = lastWriteResult.output.chapterContents;
          
          if (chapterContents && Array.isArray(chapterContents)) {
            console.log(`📚 Generated ${chapterContents.length} chapters:`);
            chapterContents.forEach((chapter: any, index: number) => {
              console.log(`  ${index + 1}. ${chapter.name} (${chapter.content?.length || 0} chars)`);
            });
            
            if (chapterContents.length >= 3) {
              console.log('🎉 SUCCESS: Multiple chapters generated correctly!');
            } else {
              console.log(`⚠️  WARNING: Only ${chapterContents.length} chapters generated, expected more`);
            }
          } else {
            console.log('❌ No chapter contents found in results');
          }
        } else {
          console.log('❌ No WriteChapters results found');
        }

        // Check for completion event
        const completionResults = result.output.filter((r: any) => 
          r.output && r.output.tutorialId
        );
        
        if (completionResults.length > 0) {
          const tutorialId = completionResults[completionResults.length - 1].output.tutorialId;
          console.log(`🔗 Tutorial ID generated: ${tutorialId}`);
          console.log('✅ Tutorial completion and database saving verified');
        } else {
          console.log('❌ No tutorial ID found - database saving may have failed');
        }
      }
    } else {
      console.error('❌ Workflow failed:', result.error?.message || 'Unknown error');
    }

    return result;

  } catch (error) {
    console.error('💥 Test execution failed:', error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testChapterGeneration()
    .then(() => {
      console.log('🏁 Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

export { testChapterGeneration };
